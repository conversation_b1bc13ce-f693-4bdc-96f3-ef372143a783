package com.fxiaoke.file.process.client;

import com.fxiaoke.file.process.config.TextInConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
@Slf4j(topic = "TextInClient")
public class TextInClient {

  private final TextInConfig config;

  public TextInClient(TextInConfig textInConfig) {
    this.config = textInConfig;
  }

}
