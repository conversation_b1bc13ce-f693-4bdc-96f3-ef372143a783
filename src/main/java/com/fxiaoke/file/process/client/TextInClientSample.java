package com.fxiaoke.file.process.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

public class TextInClientSample {
    private final String appId;
    private final String secretCode;
    private final String baseUrl;

    public TextInClientSample(String appId, String secretCode) {
        this.appId = appId;
        this.secretCode = secretCode;
        this.baseUrl = "https://api.textin.com/ai/service/v1/pdf_to_markdown";
    }

    public String recognize(byte[] fileContent, HashMap<String, Object> options) throws IOException {
        // Build URL with query parameters
        StringBuilder queryParams = new StringBuilder();
        // Add query parameters
        for (Map.Entry<String, Object> entry : options.entrySet()) {
            if (queryParams.length() > 0) {
                queryParams.append("&");
            }
            queryParams.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue().toString(), "UTF-8"));
        }

        // Create full URL with query parameters
        String fullUrl = baseUrl + (queryParams.length() > 0 ? "?" + queryParams : "");
        URL url = new URL(fullUrl);

        // Create and configure HTTP connection
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");

        // Set headers
        connection.setRequestProperty("x-ti-app-id", appId);
        connection.setRequestProperty("x-ti-secret-code", secretCode);
        // 方式一：读取本地文件
        connection.setRequestProperty("Content-Type", "application/octet-stream");
        // 方式二：使用URL方式
        // connection.setRequestProperty("Content-Type", "text/plain");

        // Enable output and send file content
        connection.setDoOutput(true);
        try (OutputStream os = connection.getOutputStream()) {
            os.write(fileContent);
            os.flush();
        }

        // Read response
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader in = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String inputLine;
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                return response.toString();
            }
        } else {
            throw new IOException("HTTP request failed with code: " + responseCode);
        }
    }

    public static void main(String[] args) throws IOException {
        TextInClientSample client = new TextInClientSample("9c0ff3859bbb6e1a7acbd2658c88f331", "bd05fdbcdf880a3498c590e5e1aa8fa7");

        // Read image file
        // 方式一：读取本地文件
        byte[] fileContent = Files.readAllBytes(Paths.get("example.png"));
        // 方式二：使用URL方式（需要将headers中的Content-Type改为'text/plain'）
        // byte[] fileContent = "https://example.com/path/to/your.pdf".getBytes(StandardCharsets.UTF_8);

        HashMap<String, Object> options = new HashMap<>();
        options.put("apply_document_tree", 1);
        options.put("apply_image_analysis", 1);
        options.put("apply_merge", 1);
        options.put("catalog_details", 1);
        options.put("dpi", 216);
        options.put("formula_level", 1);
        options.put("get_excel", 1);
        options.put("get_image", "objects");
        options.put("markdown_details", 1);
        options.put("page_count", 10);
        options.put("page_details", 1);
        options.put("page_start", 1);
        options.put("paratext_mode", "annotation");
        options.put("parse_mode", "scan");
        options.put("pdf_pwd", "123456");
        options.put("raw_ocr", 1);
        options.put("table_flavor", "md");

        try {
            String response = client.recognize(fileContent, options);
            
            // 保存完整的JSON响应到result.json文件
            Files.write(Paths.get("result.json"), response.getBytes());
            
            // 解析JSON响应以提取markdown内容
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response);
            if (jsonNode.has("result") && jsonNode.get("result").has("markdown")) {
                String markdown = jsonNode.get("result").get("markdown").asText();
                Files.write(Paths.get("result.md"), markdown.getBytes());
            }
            
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}